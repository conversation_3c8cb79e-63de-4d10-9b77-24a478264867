<script setup>
import {ElInput, ElOption, ElSelect} from 'element-plus';
import {useMessage} from "@/hooks/useMessage.js";

defineOptions({ name: 'goods:relation' })

function getData(form) {
  console.log(111111, form)
}

const selectOptions = ref([
  {label: '全部', value: '-1'},
  {label: '已关联', value: '1'},
  {label: '未关联', value: '0'}
])
const searchItem = [
  {label: '货品名称', prop: 'productName', render: 'input'},
  {label: '货品规格', prop: 'productSkuName', render: 'input'},
  {
    label: '货品编码', prop: 'productOuterId', render: (model) => {
      return h(ElInput, {
        placeholder: '货品编码/货品规格编码',
        modelValue: model.formData.productOuterId,
        'onUpdate:modelValue': (value) => {
          model.formData.productOuterId = value;
        }
      });
    }
  },
  {
    label: '关联状态', prop: 'relationStatus', render: (model) => {
      return h(ElSelect, {
          modelValue: model.formData.relationStatus,
          'onUpdate:modelValue': (value) => {
            model.formData.relationStatus = value;
          },
        },
        // 渲染 options
        selectOptions.value.map(option => {
          return h(ElOption, {
            key: option.value,
            label: option.label,
            value: option.value
          });
        }));
    }
  },
]

const searchItem2 = [
  {
    label: '店铺列表', prop: 'shopId', render: (model) => {
      return h(ElSelect, {
        modelValue: model.formData.shopId,
        'onUpdate:modelValue': (value) => {
          model.formData.shopId = value;
        },
      })
    }
  }, {label: '商品名称', prop: 'productName', render: 'input'},
  {label: '商品规格', prop: 'productSkuName', render: 'input'},
  {
    label: '商品编码', prop: 'productOuterId', render: (model) => {
      return h(ElInput, {
        placeholder: '商品编码/商品规格编码',
        modelValue: model.formData.productOuterId,
        'onUpdate:modelValue': (value) => {
          model.formData.productOuterId = value;
        }
      });
    }
  },
  {
    label: '关联状态', prop: 'relationStatus', render: (model) => {
      return h(ElSelect, {
          modelValue: model.formData.relationStatus,
          'onUpdate:modelValue': (value) => {
            model.formData.relationStatus = value;
          },
        },
        // 渲染 options
        selectOptions.value.map(option => {
          return h(ElOption, {
            key: option.value,
            label: option.label,
            value: option.value
          });
        }));
    }
  },]

const searchItem3 = [
  {
    label: '店铺列表', prop: 'shopId', render: (model) => {
      return h(ElSelect, {
        modelValue: model.formData.shopId,
        'onUpdate:modelValue': (value) => {
          model.formData.shopId = value;
        },
      })
    }
  }, {label: '商品名称', prop: 'productName', render: 'input'},
  {label: '商品规格', prop: 'productSkuName', render: 'input'},
  {
    label: '商品编码', prop: 'productOuterId', render: (model) => {
      return h(ElInput, {
        placeholder: '商品编码/商品规格编码',
        modelValue: model.formData.productOuterId,
        'onUpdate:modelValue': (value) => {
          model.formData.productOuterId = value;
        }
      });
    }
  }]

const searchItem4 = [
  {label: '货品名称', prop: 'productName', render: 'input'},
  {label: '货品规格', prop: 'productSkuName', render: 'input'},
  {
    label: '货品编码', prop: 'productOuterId', render: (model) => {
      return h(ElInput, {
        placeholder: '货品编码/货品规格编码',
        modelValue: model.formData.productOuterId,
        'onUpdate:modelValue': (value) => {
          model.formData.productOuterId = value;
        }
      });
    }
  },
]

const activeMode = ref(1)
const tableData = ref([])

onMounted(() => {
  changeMode()
})

function changeMode() {
  const result = []
  if (activeMode.value === 1) {
    //货品角度
    productList.forEach(it => {
      it.systemItemSkuList.forEach((item, idx) => {
        const obj = {...it}
        delete obj.systemItemSkuList
        obj.systemItemSku = item
        obj.rowSpan = idx ? 0 : it.systemItemSkuList.length
        result.push(obj)
      })
    })
    tableData.value = result
    return
  }
  goodsList.forEach(it => {
    it.platformSkuList.forEach((item, idx) => {
      const obj = {...it}
      delete obj.platformSkuList
      obj.platformSku = item
      obj.platformSku.bindSystemSkuList = []
      obj.rowSpan = idx ? 0 : it.platformSkuList.length
      result.push(obj)
    })
  })
  console.log(123333, result)
  tableData.value = result
}

const goodsList = [
  {
    "userId": 33294,
    "platformItemId": "3705923593209774281",
    "platformShopId": "4500272",
    "platform": 34,
    "platformItemName": "小恶魔数据线保护套可爱卡通防折断苹果安卓通用",
    "platformItemPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/RJQtjHJ_m_a7c6e9bfffddded91c7ef7789be2e809_sx_1099293_www1075-1075",
    "platformItemOuterId": "",
    "platformItemPrice": 510,
    "platformItemStatus": 0,
    "platformItemShortGoodsName": "",
    "systemItemId": 0,
    "orderNo": 0,
    "createTime": 1725705787,
    "updateTime": 1741590464,
    "deleteTime": 0,
    "extendInfo": "",
    "platformShopName": "麦町日淘",
    "platformSkuList": [
      {
        "userId": 33294,
        "platformSkuId": "3416701890157570",
        "platformItemId": "3705923593209774281",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 2182,
        "systemItemSkuId": 26835,
        "linkType": 1,
        "platformSkuName": "随机颜色,10个",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/RJQtjHJ_m_a7c6e9bfffddded91c7ef7789be2e809_sx_1099293_www1075-1075",
        "platformSkuOuterId": "test",
        "platformSkuPrice": 510,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      }
    ]
  },
  {
    "userId": 33294,
    "platformItemId": "3705919581643211126",
    "platformShopId": "4500272",
    "platform": 34,
    "platformItemName": "罐头饮料开罐器多功能开瓶器拧瓶盖器家用",
    "platformItemPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/RJQtjHJ_m_37e66c166f0b726be16f3b604d72a4c8_sx_743196_www800-800",
    "platformItemOuterId": "",
    "platformItemPrice": 1000,
    "platformItemStatus": 0,
    "platformItemShortGoodsName": "",
    "systemItemId": 0,
    "orderNo": 0,
    "createTime": 1725703921,
    "updateTime": 1735094910,
    "deleteTime": 0,
    "extendInfo": "",
    "platformShopName": "麦町日淘",
    "platformSkuList": [
      {
        "userId": 33294,
        "platformSkuId": "3416461521297922",
        "platformItemId": "3705919581643211126",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 1858,
        "systemItemSkuId": 25811,
        "linkType": 1,
        "platformSkuName": "黄色,1个",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/RJQtjHJ_m_37e66c166f0b726be16f3b604d72a4c8_sx_743196_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1000,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      }
    ]
  },
  {
    "userId": 33294,
    "platformItemId": "3679221489481154579",
    "platformShopId": "4500272",
    "platform": 34,
    "platformItemName": "超细纤维抽取式抹布一次性懒人抹布多功能吸水百洁布厨房洗碗布",
    "platformItemPicUrl": "https://p9-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_18b04b06a59026dc2bae19443fff8dd6_sx_198718_www800-800",
    "platformItemOuterId": "",
    "platformItemPrice": 882,
    "platformItemStatus": 0,
    "platformItemShortGoodsName": "",
    "systemItemId": 0,
    "orderNo": 0,
    "createTime": 1713271942,
    "updateTime": 1719897294,
    "deleteTime": 0,
    "extendInfo": "",
    "platformShopName": "麦町日淘",
    "platformSkuList": [
      {
        "userId": 33294,
        "platformSkuId": "3402117830301442",
        "platformItemId": "3679221489481154579",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 2182,
        "systemItemSkuId": 26835,
        "linkType": 1,
        "platformSkuName": "【袋装】10条-20*20cm-灰色",
        "platformSkuPicUrl": "https://p9-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_18b04b06a59026dc2bae19443fff8dd6_sx_198718_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 882,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402117830301698",
        "platformItemId": "3679221489481154579",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "【袋装】20条-20*20cm-灰色",
        "platformSkuPicUrl": "https://p9-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_18b04b06a59026dc2bae19443fff8dd6_sx_198718_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1278,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402117830301954",
        "platformItemId": "3679221489481154579",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "【袋装】20条-22*22cm-灰色",
        "platformSkuPicUrl": "https://p9-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_18b04b06a59026dc2bae19443fff8dd6_sx_198718_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1413,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402117830302210",
        "platformItemId": "3679221489481154579",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "【袋装】20条-22*22cm-蓝色",
        "platformSkuPicUrl": "https://p9-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_18b04b06a59026dc2bae19443fff8dd6_sx_198718_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1413,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402117830302466",
        "platformItemId": "3679221489481154579",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "【袋装】20条-22*22cm-粉色",
        "platformSkuPicUrl": "https://p9-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_18b04b06a59026dc2bae19443fff8dd6_sx_198718_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1413,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402117830302722",
        "platformItemId": "3679221489481154579",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "【盒装】20条-20*20cm-灰色",
        "platformSkuPicUrl": "https://p9-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_18b04b06a59026dc2bae19443fff8dd6_sx_198718_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1425,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402117830302978",
        "platformItemId": "3679221489481154579",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "【盒装】20条-22*22cm-灰色",
        "platformSkuPicUrl": "https://p9-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_18b04b06a59026dc2bae19443fff8dd6_sx_198718_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1605,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402117830303234",
        "platformItemId": "3679221489481154579",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "【盒装】20条-22*22cm-蓝色",
        "platformSkuPicUrl": "https://p9-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_18b04b06a59026dc2bae19443fff8dd6_sx_198718_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1605,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402117830303490",
        "platformItemId": "3679221489481154579",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "【盒装】20条-22*22cm-粉色",
        "platformSkuPicUrl": "https://p9-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_18b04b06a59026dc2bae19443fff8dd6_sx_198718_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1605,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402117830303746",
        "platformItemId": "3679221489481154579",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "【盒装】20条-22*22cm-咖色",
        "platformSkuPicUrl": "https://p9-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_18b04b06a59026dc2bae19443fff8dd6_sx_198718_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1605,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402117830304002",
        "platformItemId": "3679221489481154579",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "【盒装】15条-22*28cm-灰色",
        "platformSkuPicUrl": "https://p9-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_18b04b06a59026dc2bae19443fff8dd6_sx_198718_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1545,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402117830304258",
        "platformItemId": "3679221489481154579",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "【盒装】15条-22*28cm-蓝色",
        "platformSkuPicUrl": "https://p9-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_18b04b06a59026dc2bae19443fff8dd6_sx_198718_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1545,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402117830304514",
        "platformItemId": "3679221489481154579",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "【盒装】15条-22*28cm-粉色",
        "platformSkuPicUrl": "https://p9-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_18b04b06a59026dc2bae19443fff8dd6_sx_198718_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1545,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402117830304770",
        "platformItemId": "3679221489481154579",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "【盒装】15条-22*28cm-咖色",
        "platformSkuPicUrl": "https://p9-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_18b04b06a59026dc2bae19443fff8dd6_sx_198718_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1545,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      }
    ]
  },
  {
    "userId": 33294,
    "platformItemId": "3679220722896601405",
    "platformShopId": "4500272",
    "platform": 34,
    "platformItemName": "洗碗手套女加厚牛筋乳胶橡胶塑胶家务耐用防水胶皮劳保清洁洗衣服",
    "platformItemPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
    "platformItemOuterId": "",
    "platformItemPrice": 468,
    "platformItemStatus": 0,
    "platformItemShortGoodsName": "",
    "systemItemId": 0,
    "orderNo": 0,
    "createTime": 1713271942,
    "updateTime": 1720596287,
    "deleteTime": 0,
    "extendInfo": "",
    "platformShopName": "麦町日淘",
    "platformSkuList": [
      {
        "userId": 33294,
        "platformSkuId": "3402274922483458",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "牛筋本色加长38cm（约98g）,S",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 768,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922483714",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "牛筋本色加长38cm（约98g）,L",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 768,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922483970",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "牛筋本色加长38cm（约98g）,M",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 768,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922484226",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "55长橘色乳胶手套,S",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1368,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922484482",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "55长橘色乳胶手套,L",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1368,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922484738",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "55长橘色乳胶手套,M",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1368,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922484994",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "45长大红乳胶手套,S",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 499,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922485250",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "45长大红乳胶手套,L",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 804,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922485506",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "45长大红乳胶手套,M",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 499,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922485762",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "加长粉色乳胶38cm,S",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1368,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922486018",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "加长粉色乳胶38cm,L",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 624,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922486274",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "加长粉色乳胶38cm,M",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1368,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922486530",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "牛筋乳胶31cm（约80g）,S",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 612,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922486786",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "牛筋乳胶31cm（约80g）,L",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 612,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922487042",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "牛筋乳胶31cm（约80g）,M",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 612,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922487298",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "加长大红乳胶38cm,S",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 768,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922487554",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "加长大红乳胶38cm,L",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 768,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922487810",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "加长大红乳胶38cm,M",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 768,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922488066",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "黄色 PVC款30cm（约70g）,S",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 468,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922488322",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "黄色 PVC款30cm（约70g）,L",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 468,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922488578",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "黄色 PVC款30cm（约70g）,M",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 468,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922488834",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "牛筋乳胶27cm（约45g）,S",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1368,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922489090",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "牛筋乳胶27cm（约45g）,L",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 492,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922489346",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "牛筋乳胶27cm（约45g）,M",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 492,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922489602",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "牛筋本色加长45cm（约148g）,S",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 876,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922489858",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "牛筋本色加长45cm（约148g）,L",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 876,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922490114",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "牛筋本色加长45cm（约148g）,M",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 876,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922490370",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "45长橘红牛筋手套,S",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 888,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922490626",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "45长橘红牛筋手套,L",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 888,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922490882",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "45长橘红牛筋手套,M",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 888,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922491138",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "加绒45长大红乳胶手套,S",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1368,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922491394",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "加绒45长大红乳胶手套,L",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 864,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922491650",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "加绒45长大红乳胶手套,M",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1368,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922491906",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "特厚牛筋31cm（约100g）,S",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 684,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922492162",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "特厚牛筋31cm（约100g）,L",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 684,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402274922492418",
        "platformItemId": "3679220722896601405",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "特厚牛筋31cm（约100g）,M",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_c6885ca85babe38bf46eb379aeaef282_sx_151406_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 684,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      }
    ]
  },
  {
    "userId": 33294,
    "platformItemId": "3679217596051357998",
    "platformShopId": "4500272",
    "platform": 34,
    "platformItemName": "双面银丝洗碗机双层加厚加棉厨房抹布吸水清洁家用清洁帮手不沾油",
    "platformItemPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_b0f1893e628076b1e7f0a4ec87ca0e82_sx_174375_www591-591",
    "platformItemOuterId": "",
    "platformItemPrice": 318,
    "platformItemStatus": 0,
    "platformItemShortGoodsName": "",
    "systemItemId": 0,
    "orderNo": 0,
    "createTime": 1713270620,
    "updateTime": 1719935602,
    "deleteTime": 0,
    "extendInfo": "",
    "platformShopName": "麦町日淘",
    "platformSkuList": [
      {
        "userId": 33294,
        "platformSkuId": "3402291820930050",
        "platformItemId": "3679217596051357998",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "20*20 单层锁边",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_b0f1893e628076b1e7f0a4ec87ca0e82_sx_174375_www591-591",
        "platformSkuOuterId": "",
        "platformSkuPrice": 318,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402291820930306",
        "platformItemId": "3679217596051357998",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "20*20 双层锁边",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_b0f1893e628076b1e7f0a4ec87ca0e82_sx_174375_www591-591",
        "platformSkuOuterId": "",
        "platformSkuPrice": 354,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402291820930562",
        "platformItemId": "3679217596051357998",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "20*20 单层锁边【2片装】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_b0f1893e628076b1e7f0a4ec87ca0e82_sx_174375_www591-591",
        "platformSkuOuterId": "",
        "platformSkuPrice": 400,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402291820930818",
        "platformItemId": "3679217596051357998",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "20*20 单层锁边【4片装】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_b0f1893e628076b1e7f0a4ec87ca0e82_sx_174375_www591-591",
        "platformSkuOuterId": "",
        "platformSkuPrice": 544,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402291820931074",
        "platformItemId": "3679217596051357998",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "20*20 单层锁边【5片装】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_b0f1893e628076b1e7f0a4ec87ca0e82_sx_174375_www591-591",
        "platformSkuOuterId": "",
        "platformSkuPrice": 621,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402291820931330",
        "platformItemId": "3679217596051357998",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "20*20 单层锁边【10片装】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_b0f1893e628076b1e7f0a4ec87ca0e82_sx_174375_www591-591",
        "platformSkuOuterId": "",
        "platformSkuPrice": 942,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402291820931586",
        "platformItemId": "3679217596051357998",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "20*20 双层锁边【2片装】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_b0f1893e628076b1e7f0a4ec87ca0e82_sx_174375_www591-591",
        "platformSkuOuterId": "",
        "platformSkuPrice": 474,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402291820931842",
        "platformItemId": "3679217596051357998",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "20*20 双层锁边【4片装】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_b0f1893e628076b1e7f0a4ec87ca0e82_sx_174375_www591-591",
        "platformSkuOuterId": "",
        "platformSkuPrice": 660,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402291820932098",
        "platformItemId": "3679217596051357998",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "20*20 双层锁边【5片装】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_b0f1893e628076b1e7f0a4ec87ca0e82_sx_174375_www591-591",
        "platformSkuOuterId": "",
        "platformSkuPrice": 786,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402291820932354",
        "platformItemId": "3679217596051357998",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "20*20 双层锁边【10片装】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_b0f1893e628076b1e7f0a4ec87ca0e82_sx_174375_www591-591",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1308,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      }
    ]
  },
  {
    "userId": 33294,
    "platformItemId": "3679217142923919722",
    "platformShopId": "4500272",
    "platform": 34,
    "platformItemName": "魔力无痕抹布擦玻璃无水印擦镜子神奇抹布家务清洁百洁布",
    "platformItemPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8f6bb73fa64d684aabb538ce420e7b06_sx_120351_www800-800",
    "platformItemOuterId": "",
    "platformItemPrice": 309,
    "platformItemStatus": 0,
    "platformItemShortGoodsName": "",
    "systemItemId": 0,
    "orderNo": 0,
    "createTime": 1713270620,
    "updateTime": 1719829369,
    "deleteTime": 0,
    "extendInfo": "",
    "platformShopName": "麦町日淘",
    "platformSkuList": [
      {
        "userId": 33294,
        "platformSkuId": "3402117660773378",
        "platformItemId": "3679217142923919722",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "神奇魔力布-灰色-30*30",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8f6bb73fa64d684aabb538ce420e7b06_sx_120351_www800-800",
        "platformSkuOuterId": "这个是code吗？",
        "platformSkuPrice": 309,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      }
    ]
  },
  {
    "userId": 33294,
    "platformItemId": "3679217138679283957",
    "platformShopId": "4500272",
    "platform": 34,
    "platformItemName": "厨房用纸干湿两用懒人抹布不掉毛洗碗布不沾油厨房巾一次性",
    "platformItemPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_ca962bce52587969262120ba24df36a2_sx_271120_www1000-1000",
    "platformItemOuterId": "",
    "platformItemPrice": 453,
    "platformItemStatus": 0,
    "platformItemShortGoodsName": "",
    "systemItemId": 0,
    "orderNo": 0,
    "createTime": 1713270620,
    "updateTime": 1719437334,
    "deleteTime": 0,
    "extendInfo": "",
    "platformShopName": "麦町日淘",
    "platformSkuList": [
      {
        "userId": 33294,
        "platformSkuId": "3402225661296386",
        "platformItemId": "3679217138679283957",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "散装10片【常规款】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_ca962bce52587969262120ba24df36a2_sx_271120_www1000-1000",
        "platformSkuOuterId": "",
        "platformSkuPrice": 453,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225661296642",
        "platformItemId": "3679217138679283957",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "20*20【50张】常规款",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_ca962bce52587969262120ba24df36a2_sx_271120_www1000-1000",
        "platformSkuOuterId": "",
        "platformSkuPrice": 548,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225661296898",
        "platformItemId": "3679217138679283957",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "20*20【100张】常规款",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_ca962bce52587969262120ba24df36a2_sx_271120_www1000-1000",
        "platformSkuOuterId": "",
        "platformSkuPrice": 674,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225661297154",
        "platformItemId": "3679217138679283957",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "20*20【200张】常规款",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_ca962bce52587969262120ba24df36a2_sx_271120_www1000-1000",
        "platformSkuOuterId": "",
        "platformSkuPrice": 840,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225661297410",
        "platformItemId": "3679217138679283957",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "25*25【100张】常规印花款",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_ca962bce52587969262120ba24df36a2_sx_271120_www1000-1000",
        "platformSkuOuterId": "",
        "platformSkuPrice": 787,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225661297666",
        "platformItemId": "3679217138679283957",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "25*25【200张】常规款印花款",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_ca962bce52587969262120ba24df36a2_sx_271120_www1000-1000",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1050,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225661297922",
        "platformItemId": "3679217138679283957",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "20*20【50张】常规款白色",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_ca962bce52587969262120ba24df36a2_sx_271120_www1000-1000",
        "platformSkuOuterId": "",
        "platformSkuPrice": 546,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225661298178",
        "platformItemId": "3679217138679283957",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "20*20【100张】常规款白色",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_ca962bce52587969262120ba24df36a2_sx_271120_www1000-1000",
        "platformSkuOuterId": "",
        "platformSkuPrice": 669,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225661298434",
        "platformItemId": "3679217138679283957",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "20*20【200张】常规款白色",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_ca962bce52587969262120ba24df36a2_sx_271120_www1000-1000",
        "platformSkuOuterId": "",
        "platformSkuPrice": 828,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225661298690",
        "platformItemId": "3679217138679283957",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "25*25【100张】常规款白色",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_ca962bce52587969262120ba24df36a2_sx_271120_www1000-1000",
        "platformSkuOuterId": "",
        "platformSkuPrice": 774,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225661298946",
        "platformItemId": "3679217138679283957",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "25*25【200张】常规款白色",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_ca962bce52587969262120ba24df36a2_sx_271120_www1000-1000",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1032,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225661299202",
        "platformItemId": "3679217138679283957",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "25*25【50张】加厚款印花款",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_ca962bce52587969262120ba24df36a2_sx_271120_www1000-1000",
        "platformSkuOuterId": "",
        "platformSkuPrice": 812,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225661299458",
        "platformItemId": "3679217138679283957",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "25*25【50张】加厚款白色",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_ca962bce52587969262120ba24df36a2_sx_271120_www1000-1000",
        "platformSkuOuterId": "",
        "platformSkuPrice": 798,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225661299714",
        "platformItemId": "3679217138679283957",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "线条30*30【40张】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_ca962bce52587969262120ba24df36a2_sx_271120_www1000-1000",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1485,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225661299970",
        "platformItemId": "3679217138679283957",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "格纹30*30【40张】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_ca962bce52587969262120ba24df36a2_sx_271120_www1000-1000",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1485,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225661300226",
        "platformItemId": "3679217138679283957",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "六角纹30*30【40张】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_ca962bce52587969262120ba24df36a2_sx_271120_www1000-1000",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1485,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225661300482",
        "platformItemId": "3679217138679283957",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "25*25【50张】加厚款  颜色随机",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_ca962bce52587969262120ba24df36a2_sx_271120_www1000-1000",
        "platformSkuOuterId": "",
        "platformSkuPrice": 576,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225661300738",
        "platformItemId": "3679217138679283957",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "25*20【50张】常规款",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_ca962bce52587969262120ba24df36a2_sx_271120_www1000-1000",
        "platformSkuOuterId": "",
        "platformSkuPrice": 615,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225661300994",
        "platformItemId": "3679217138679283957",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "抽取式懒人抹布【花朵款】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_ca962bce52587969262120ba24df36a2_sx_271120_www1000-1000",
        "platformSkuOuterId": "",
        "platformSkuPrice": 895,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      }
    ]
  },
  {
    "userId": 33294,
    "platformItemId": "3679216341946073473",
    "platformShopId": "4500272",
    "platform": 34,
    "platformItemName": "懒人清洁抹布一次性洗碗布厨房纸巾干湿两用无纺布加厚可水洗",
    "platformItemPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_f5655d976b4ee30f6831aad09f4cb27d_sx_134700_www800-800",
    "platformItemOuterId": "",
    "platformItemPrice": 450,
    "platformItemStatus": 0,
    "platformItemShortGoodsName": "",
    "systemItemId": 0,
    "orderNo": 0,
    "createTime": 1713270620,
    "updateTime": 1719437514,
    "deleteTime": 0,
    "extendInfo": "",
    "platformShopName": "麦町日淘",
    "platformSkuList": [
      {
        "userId": 33294,
        "platformSkuId": "3402225966999042",
        "platformItemId": "3679216341946073473",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "【印花款】图案随机,规格【25x20*50】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_f5655d976b4ee30f6831aad09f4cb27d_sx_134700_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 606,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225966999298",
        "platformItemId": "3679216341946073473",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "【印花款】图案随机,规格【25x25x50张】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_f5655d976b4ee30f6831aad09f4cb27d_sx_134700_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 648,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225966999554",
        "platformItemId": "3679216341946073473",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "【印花款】图案随机,规格【20x25*50】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_f5655d976b4ee30f6831aad09f4cb27d_sx_134700_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 606,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225966999810",
        "platformItemId": "3679216341946073473",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "【印花款】图案随机,规格【20x20x50张】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_f5655d976b4ee30f6831aad09f4cb27d_sx_134700_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 552,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225967000066",
        "platformItemId": "3679216341946073473",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "【印花款】图案随机,规格【20x20x80张】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_f5655d976b4ee30f6831aad09f4cb27d_sx_134700_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 660,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225967000322",
        "platformItemId": "3679216341946073473",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "【印花款】图案随机,规格【20x20x100张】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_f5655d976b4ee30f6831aad09f4cb27d_sx_134700_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 696,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225967000578",
        "platformItemId": "3679216341946073473",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "【印花款】图案随机,规格【20x20x200张】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_f5655d976b4ee30f6831aad09f4cb27d_sx_134700_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 816,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225967000834",
        "platformItemId": "3679216341946073473",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "【印花款】图案随机,规格【20x20x300张】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_f5655d976b4ee30f6831aad09f4cb27d_sx_134700_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 894,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225967001090",
        "platformItemId": "3679216341946073473",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "【印花款】图案随机,抹布【不锈钢挂钩】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_f5655d976b4ee30f6831aad09f4cb27d_sx_134700_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 450,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225967001346",
        "platformItemId": "3679216341946073473",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "纯白款,规格【25x20*50】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_f5655d976b4ee30f6831aad09f4cb27d_sx_134700_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 594,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225967001602",
        "platformItemId": "3679216341946073473",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "纯白款,规格【25x25x50张】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_f5655d976b4ee30f6831aad09f4cb27d_sx_134700_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 636,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225967001858",
        "platformItemId": "3679216341946073473",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "纯白款,规格【20x25*50】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_f5655d976b4ee30f6831aad09f4cb27d_sx_134700_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 594,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225967002114",
        "platformItemId": "3679216341946073473",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "纯白款,规格【20x20x50张】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_f5655d976b4ee30f6831aad09f4cb27d_sx_134700_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 540,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225967002370",
        "platformItemId": "3679216341946073473",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "纯白款,规格【20x20x80张】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_f5655d976b4ee30f6831aad09f4cb27d_sx_134700_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 648,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225967002626",
        "platformItemId": "3679216341946073473",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "纯白款,规格【20x20x100张】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_f5655d976b4ee30f6831aad09f4cb27d_sx_134700_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 684,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225967002882",
        "platformItemId": "3679216341946073473",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "纯白款,规格【20x20x200张】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_f5655d976b4ee30f6831aad09f4cb27d_sx_134700_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 804,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225967003138",
        "platformItemId": "3679216341946073473",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "纯白款,规格【20x20x300张】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_f5655d976b4ee30f6831aad09f4cb27d_sx_134700_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 882,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3402225967003394",
        "platformItemId": "3679216341946073473",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "纯白款,抹布【不锈钢挂钩】",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_f5655d976b4ee30f6831aad09f4cb27d_sx_134700_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 450,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      }
    ]
  },
  {
    "userId": 33294,
    "platformItemId": "3675092593638768736",
    "platformShopId": "4500272",
    "platform": 34,
    "platformItemName": "魔力无痕抹布擦玻璃无水印擦镜子神奇抹布家务清洁百洁布",
    "platformItemPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8f6bb73fa64d684aabb538ce420e7b06_sx_120351_www800-800",
    "platformItemOuterId": "",
    "platformItemPrice": 309,
    "platformItemStatus": 0,
    "platformItemShortGoodsName": "",
    "systemItemId": 0,
    "orderNo": 0,
    "createTime": 1711348873,
    "updateTime": 1714454007,
    "deleteTime": 0,
    "extendInfo": "",
    "platformShopName": "麦町日淘",
    "platformSkuList": [
      {
        "userId": 33294,
        "platformSkuId": "3400494569080322",
        "platformItemId": "3675092593638768736",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "神奇魔力布-灰色-30*30",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8f6bb73fa64d684aabb538ce420e7b06_sx_120351_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 309,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      }
    ]
  },
  {
    "userId": 33294,
    "platformItemId": "3667325190372721116",
    "platformShopId": "4500272",
    "platform": 34,
    "platformItemName": "珊瑚绒双面双色复合方巾高密度洗车擦车毛巾纤维加厚吸水清洁抹布",
    "platformItemPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
    "platformItemOuterId": "",
    "platformItemPrice": 384,
    "platformItemStatus": 0,
    "platformItemShortGoodsName": "",
    "systemItemId": 0,
    "orderNo": 0,
    "createTime": 1707741040,
    "updateTime": 1741339655,
    "deleteTime": 0,
    "extendInfo": "",
    "platformShopName": "麦町日淘",
    "platformSkuList": [
      {
        "userId": 33294,
        "platformSkuId": "3397680997692674",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+黄色（25*25）,裸装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 390,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997692930",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+黄色（25*25）,1条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 396,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997693186",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+黄色（25*25）,3条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 576,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997693442",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+黄色（25*25）,5条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 744,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997693698",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+黄色（25*25）,10条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1182,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997693954",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+黄色（25*25）,15条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1620,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997694210",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+黄色（25*25）,20条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 2058,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997694466",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+红色（25*25）,裸装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 390,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997694722",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+红色（25*25）,1条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 396,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997694978",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+红色（25*25）,3条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 576,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997695234",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+红色（25*25）,5条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 744,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997695490",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+红色（25*25）,10条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1182,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997695746",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+红色（25*25）,15条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1620,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997696002",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+红色（25*25）,20条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 2058,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997696258",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+蓝色（25*25）,裸装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 390,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997696514",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+蓝色（25*25）,1条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 396,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997696770",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+蓝色（25*25）,3条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 576,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997697026",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+蓝色（25*25）,5条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 744,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997697282",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+蓝色（25*25）,10条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1182,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997697538",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+蓝色（25*25）,15条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1620,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997697794",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+蓝色（25*25）,20条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 2058,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997698050",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+灰（25*25）,裸装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 384,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997698306",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+灰（25*25）,1条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 390,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997698562",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+灰（25*25）,3条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 570,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997698818",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+灰（25*25）,5条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 738,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997699074",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+灰（25*25）,10条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1176,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997699330",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+灰（25*25）,15条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1614,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997699586",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+灰（25*25）,20条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 2052,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997699842",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "颜色随机（25*25）,裸装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 420,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997700098",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "颜色随机（25*25）,1条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 426,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997700354",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "颜色随机（25*25）,3条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 678,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997700610",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "颜色随机（25*25）,5条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 918,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997700866",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "颜色随机（25*25）,10条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1536,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997701122",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "颜色随机（25*25）,15条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 2154,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997701378",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "颜色随机（25*25）,20条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 2772,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997701634",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+黄色（30*30）,裸装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 420,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997701890",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+黄色（30*30）,1条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 426,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997702146",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+黄色（30*30）,3条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 678,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997702402",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+黄色（30*30）,5条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 918,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997702658",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+黄色（30*30）,10条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1536,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997702914",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+黄色（30*30）,15条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 2154,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997703170",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+黄色（30*30）,20条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 2772,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997703426",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+红色（30*30）,裸装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 420,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997703682",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+红色（30*30）,1条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 426,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997703938",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+红色（30*30）,3条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 678,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997704194",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+红色（30*30）,5条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 918,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997704450",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+红色（30*30）,10条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1536,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997704706",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+红色（30*30）,15条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 2154,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997704962",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+红色（30*30）,20条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 2772,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997705218",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+蓝色（30*30）,裸装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 420,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997705474",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+蓝色（30*30）,1条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 426,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997705730",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+蓝色（30*30）,3条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 678,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997705986",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+蓝色（30*30）,5条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 918,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997706242",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+蓝色（30*30）,10条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1536,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997706498",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+蓝色（30*30）,15条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 2154,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997706754",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+蓝色（30*30）,20条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 2772,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997707010",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+灰（30*30）,裸装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 420,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997707266",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+灰（30*30）,1条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 426,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997707522",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+灰（30*30）,3条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 678,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997707778",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+灰（30*30）,5条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 918,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997708034",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+灰（30*30）,10条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1536,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997708290",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+灰（30*30）,15条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 2154,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997708546",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "灰+灰（30*30）,20条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 2772,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997708802",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "颜色随机（30*30）,裸装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 426,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997709058",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "颜色随机（30*30）,1条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 432,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997709314",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "颜色随机（30*30）,3条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 684,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997709570",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "颜色随机（30*30）,5条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 924,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997709826",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "颜色随机（30*30）,10条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 1542,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997710082",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "颜色随机（30*30）,15条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 2160,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      },
      {
        "userId": 33294,
        "platformSkuId": "3397680997710338",
        "platformItemId": "3667325190372721116",
        "platformShopId": "4500272",
        "platform": 34,
        "systemItemId": 0,
        "systemItemSkuId": 0,
        "linkType": 1,
        "platformSkuName": "颜色随机（30*30）,20条装",
        "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_8fb381e5f52cc9d561459efd9f14866a_sx_235665_www800-800",
        "platformSkuOuterId": "",
        "platformSkuPrice": 2778,
        "platformSkuBarCode": "",
        "platformSkuStatus": 0,
        "orderNo": 0,
        "platformSkuShortGoodsName": "",
        "platformSkuWeight": 0,
        "platformSkuVolume": 0,
        "platformSkuFactoryUserId": 0,
        "platformSkuCostPrice": 0,
        "platformSkuStorageName": "",
        "platformSkuAutoSplitThreshold": 0,
        "createTime": 0,
        "updateTime": 0,
        "deleteTime": 0,
        "extendInfo": ""
      }
    ]
  }
]

const productList = [
  {
    "systemItemId": 2182,
    "systemItemName": "罐头饮料开罐器多功能开瓶器拧瓶盖器家用",
    "systemItemPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/RJQtjHJ_m_37e66c166f0b726be16f3b604d72a4c8_sx_743196_www800-800",
    "systemItemShortGoodsName": "",
    "systemItemBarCode": "",
    "systemItemOuterId": "",
    "systemItemWeight": 0,
    "systemItemVolume": 0,
    "systemItemStorageName": "",
    "systemItemCostPrice": 0,
    "systemItemPrice": 0,
    "systemItemStatus": 0,
    "userId": 33294,
    "orderNo": 0,
    "createTime": 1748503908,
    "updateTime": 1748503908,
    "deleteTime": 0,
    "systemOuterId": "MMR6909",
    "systemItemSkuList": [
      {
        "systemSkuId": 26835,
        "systemItemId": 2182,
        "systemSkuName": "黄色,1个",
        "systemSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/RJQtjHJ_m_37e66c166f0b726be16f3b604d72a4c8_sx_743196_www800-800",
        "systemSkuShortGoodsName": "",
        "systemSkuOuterId": "MMR6909-01",
        "systemSkuBarCode": "",
        "systemSkuWeight": 0,
        "systemSkuVolume": 0,
        "systemSkuStorageName": "A-1-10",
        "systemSkuCostPrice": 0,
        "systemSkuPrice": 1000,
        "systemSkuFactoryUserId": 0,
        "systemSkuStatus": 0,
        "systemSkuAutoSplitThreshold": 2,
        "userId": 33294,
        "orderNo": 0,
        "createTime": 1748503908,
        "updateTime": 1748503908,
        "deleteTime": 0,
        "systemSkuFactoryUserName": null,
        "bindPlatformSkuList": [
          {
            "userId": 33294,
            "platformSkuId": "3402117830301442",
            "platformItemId": "3679221489481154579",
            "platformShopId": "4500272",
            "platform": 34,
            "systemItemId": 2182,
            "systemItemSkuId": 26835,
            "linkType": 1,
            "platformSkuName": "【袋装】10条-20*20cm-灰色",
            "platformSkuPicUrl": "https://p9-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_18b04b06a59026dc2bae19443fff8dd6_sx_198718_www800-800",
            "platformSkuOuterId": "",
            "platformSkuPrice": 882,
            "platformSkuBarCode": "",
            "platformSkuStatus": 0,
            "orderNo": 0,
            "platformSkuShortGoodsName": "",
            "platformSkuWeight": 0,
            "platformSkuVolume": 0,
            "platformSkuFactoryUserId": 0,
            "platformSkuCostPrice": 0,
            "platformSkuStorageName": "",
            "platformSkuAutoSplitThreshold": 0,
            "createTime": 0,
            "updateTime": 0,
            "deleteTime": 0,
            "extendInfo": "",
            "platformItemName": "超细纤维抽取式抹布一次性懒人抹布多功能吸水百洁布厨房洗碗布",
            "platformItemShortGoodsName": null,
            "platformItemExtendInfo": null,
            "platformShopName": "麦町日淘",
            "platformItemPicUrl": "https://p9-aio.ecombdimg.com/obj/ecom-shop-material/GJftIdDN_m_18b04b06a59026dc2bae19443fff8dd6_sx_198718_www800-800",
            "notRelateSystemItemSkuId": 0,
            "platformItemIdList": null,
            "platformSkuIdList": null,
            "systemItemSkuIdList": null,
            "platformInfoList": null
          },
          {
            "userId": 33294,
            "platformSkuId": "3416701890157570",
            "platformItemId": "3705923593209774281",
            "platformShopId": "4500272",
            "platform": 34,
            "systemItemId": 2182,
            "systemItemSkuId": 26835,
            "linkType": 1,
            "platformSkuName": "随机颜色,10个",
            "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/RJQtjHJ_m_a7c6e9bfffddded91c7ef7789be2e809_sx_1099293_www1075-1075",
            "platformSkuOuterId": "test",
            "platformSkuPrice": 510,
            "platformSkuBarCode": "",
            "platformSkuStatus": 0,
            "orderNo": 0,
            "platformSkuShortGoodsName": "",
            "platformSkuWeight": 0,
            "platformSkuVolume": 0,
            "platformSkuFactoryUserId": 0,
            "platformSkuCostPrice": 0,
            "platformSkuStorageName": "",
            "platformSkuAutoSplitThreshold": 0,
            "createTime": 0,
            "updateTime": 0,
            "deleteTime": 0,
            "extendInfo": "",
            "platformItemName": "小恶魔数据线保护套可爱卡通防折断苹果安卓通用",
            "platformItemShortGoodsName": null,
            "platformItemExtendInfo": null,
            "platformShopName": "麦町日淘",
            "platformItemPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/RJQtjHJ_m_a7c6e9bfffddded91c7ef7789be2e809_sx_1099293_www1075-1075",
            "notRelateSystemItemSkuId": 0,
            "platformItemIdList": null,
            "platformSkuIdList": null,
            "systemItemSkuIdList": null,
            "platformInfoList": null
          }
        ]
      },
      {
        "systemSkuId": 27364,
        "systemItemId": 2182,
        "systemSkuName": "福利",
        "systemSkuPicUrl": "https://osscdn.fhd001.com/fhdportal/typimg/e6bb640b31f445e9ad364fd1ff5a22eb.png",
        "systemSkuShortGoodsName": "",
        "systemSkuOuterId": "MMR6909-02",
        "systemSkuBarCode": "",
        "systemSkuWeight": 0,
        "systemSkuVolume": 0,
        "systemSkuStorageName": null,
        "systemSkuCostPrice": 0,
        "systemSkuPrice": 500,
        "systemSkuFactoryUserId": 0,
        "systemSkuStatus": 0,
        "systemSkuAutoSplitThreshold": 0,
        "userId": 33294,
        "orderNo": 0,
        "createTime": 1750830435,
        "updateTime": 1750830435,
        "deleteTime": 0,
        "systemSkuFactoryUserName": null,
        "bindPlatformSkuList": []
      }
    ]
  },
  {
    "systemItemId": 2181,
    "systemItemName": "小恶魔数据线保护套可爱卡通防折断苹果安卓通用",
    "systemItemPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/RJQtjHJ_m_a7c6e9bfffddded91c7ef7789be2e809_sx_1099293_www1075-1075",
    "systemItemShortGoodsName": "",
    "systemItemBarCode": "",
    "systemItemOuterId": "",
    "systemItemWeight": 0,
    "systemItemVolume": 0,
    "systemItemStorageName": "",
    "systemItemCostPrice": 0,
    "systemItemPrice": 0,
    "systemItemStatus": 0,
    "userId": 33294,
    "orderNo": 0,
    "createTime": 1748503745,
    "updateTime": 1748503745,
    "deleteTime": 0,
    "systemItemSkuList": [
      {
        "systemSkuId": 26834,
        "systemItemId": 2181,
        "systemSkuName": "随机颜色,10个",
        "systemSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/RJQtjHJ_m_a7c6e9bfffddded91c7ef7789be2e809_sx_1099293_www1075-1075",
        "systemSkuShortGoodsName": null,
        "systemSkuOuterId": "test",
        "systemSkuBarCode": "",
        "systemSkuWeight": 0,
        "systemSkuVolume": 0,
        "systemSkuStorageName": "",
        "systemSkuCostPrice": 0,
        "systemSkuPrice": 510,
        "systemSkuFactoryUserId": 0,
        "systemSkuStatus": 0,
        "systemSkuAutoSplitThreshold": 0,
        "userId": 33294,
        "orderNo": 0,
        "createTime": 1748503745,
        "updateTime": 1748503745,
        "deleteTime": 0,
        "systemSkuFactoryUserName": null,
        "bindPlatformSkuList": []
      }
    ]
  },
  {
    "systemItemId": 1858,
    "systemItemName": "袜子",
    "systemItemPicUrl": null,
    "systemItemShortGoodsName": null,
    "systemItemBarCode": null,
    "systemItemOuterId": null,
    "systemItemWeight": 0,
    "systemItemVolume": 0,
    "systemItemStorageName": null,
    "systemItemCostPrice": 0,
    "systemItemPrice": 0,
    "systemItemStatus": 0,
    "userId": 33294,
    "orderNo": 0,
    "createTime": 1744977759,
    "updateTime": 1744977759,
    "deleteTime": 0,
    "systemItemSkuList": [
      {
        "systemSkuId": 25811,
        "systemItemId": 1858,
        "systemSkuName": "2",
        "systemSkuPicUrl": "",
        "systemSkuShortGoodsName": "3",
        "systemSkuOuterId": "1",
        "systemSkuBarCode": "1",
        "systemSkuWeight": 0,
        "systemSkuVolume": 0,
        "systemSkuStorageName": null,
        "systemSkuCostPrice": 0,
        "systemSkuPrice": 0,
        "systemSkuFactoryUserId": 0,
        "systemSkuStatus": 0,
        "systemSkuAutoSplitThreshold": 0,
        "userId": 33294,
        "orderNo": 0,
        "createTime": 1744977759,
        "updateTime": 1744977759,
        "deleteTime": 0,
        "systemSkuFactoryUserName": null,
        "bindPlatformSkuList": [
          {
            "userId": 33294,
            "platformSkuId": "3416461521297922",
            "platformItemId": "3705919581643211126",
            "platformShopId": "4500272",
            "platform": 34,
            "systemItemId": 1858,
            "systemItemSkuId": 25811,
            "linkType": 1,
            "platformSkuName": "黄色,1个",
            "platformSkuPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/RJQtjHJ_m_37e66c166f0b726be16f3b604d72a4c8_sx_743196_www800-800",
            "platformSkuOuterId": "",
            "platformSkuPrice": 1000,
            "platformSkuBarCode": "",
            "platformSkuStatus": 0,
            "orderNo": 0,
            "platformSkuShortGoodsName": "",
            "platformSkuWeight": 0,
            "platformSkuVolume": 0,
            "platformSkuFactoryUserId": 0,
            "platformSkuCostPrice": 0,
            "platformSkuStorageName": "",
            "platformSkuAutoSplitThreshold": 0,
            "createTime": 0,
            "updateTime": 0,
            "deleteTime": 0,
            "extendInfo": "",
            "platformItemName": "罐头饮料开罐器多功能开瓶器拧瓶盖器家用",
            "platformItemShortGoodsName": null,
            "platformItemExtendInfo": null,
            "platformShopName": "麦町日淘",
            "platformItemPicUrl": "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/RJQtjHJ_m_37e66c166f0b726be16f3b604d72a4c8_sx_743196_www800-800",
            "notRelateSystemItemSkuId": 0,
            "platformItemIdList": null,
            "platformSkuIdList": null,
            "systemItemSkuIdList": null,
            "platformInfoList": null
          }
        ]
      }
    ]
  }
]


function tableRowClassName({row, rowIndex}) {
  // 根据行数据返回类名
  if (rowIndex === 0) {
    return activeMode.value === 1 ? 'first-row-1' : 'first-row-2';
  }
  return '';
}

function objectSpanMethod({
                            row,
                            column,
                            rowIndex,
                            columnIndex,
                          }) {
  if (columnIndex === 0) {
    if (row.rowSpan > 0) {
      return {
        rowspan: row.rowSpan,
        colspan: 1,
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      }
    }
  }
  return {
    rowspan: 1,
    colspan: 1,
  }
}

function objectSpanMethod2({
                            row,
                            column,
                            rowIndex,
                            columnIndex,
                          }) {
  if ([3,4].includes(columnIndex)) {
    if (row.rowSpan > 0) {
      return {
        rowspan: row.rowSpan,
        colspan: 1,
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      }
    }
  }
  return {
    rowspan: 1,
    colspan: 1,
  }
}

const msg = useMessage()

function cancelGoodsRelation(item, row) {
  msg.delConfirm('取消关联后可以手动重新关联，确定取消关联吗？', '取消关联商品提醒').then(async () => {
    row.systemItemSku.bindPlatformSkuList = row.systemItemSku.bindPlatformSkuList.filter(it => it.platformSkuId !== item.platformSkuId)
  })
}

function cancelProductsRelation(item, row) {
  msg.delConfirm('取消关联后可以手动重新关联，确定取消关联吗？', '取消关联货品提醒').then(async () => {
    row.platformSku.bindSystemSkuList = row.platformSku.bindSystemSkuList.filter(it => it.systemSkuId !== item.systemSkuId)
  })
}

const modalObj = ref({
  visible: false,
  title: '',
  width: '500px',
  form: {},
  type: '',
})

const canChooseGoods = ref([])
const canChooseProducts = ref([])

function bindGoods(row) {
  //获取所有已经被绑的商品 sku
  const goods = []
  row.systemItemSku.bindPlatformSkuList.forEach(it => {
    goods.push(it.platformSkuId)
  })
  const result = []
  goodsList.forEach(it=>{
    const list = it.platformSkuList.filter(item=>!goods.includes(item.platformSkuId))
    list?.forEach((item, idx) => {
      const obj = {...it}
      delete obj.platformSkuList
      obj.platformSku = item
      obj.rowSpan = idx ? 0 : it.platformSkuList.length
      result.push(obj)
    })
  })
  canChooseGoods.value = result
  modalObj.value = {
    visible: true,
    title: '添加要关联的平台商品',
    width: '800px',
    form: {
      systemSkuId: row.systemItemSku.systemSkuId
    },
    type: 'bindGoods',
  }
}

function bindProducts(row) {
  //获取所有已经被绑的货品 sku
  const products = []
  row.platformSku.bindSystemSkuList.forEach(it => {
    products.push(it.systemSkuId)
  })
  const result = []
  productList.forEach(it => {
    const list = it.systemItemSkuList.filter(item => !products.includes(item.systemSkuId))
    list?.forEach((item, idx) => {
      const obj = {...it}
      delete obj.systemItemSkuList
      obj.systemItemSku = item
      obj.rowSpan = idx ? 0 : it.systemItemSkuList.length
      result.push(obj)
    })
  })
  canChooseProducts.value = result
  modalObj.value = {
    visible: true,
    title: '添加要关联的系统货品',
    width: '800px',
    form: {
      platformSkuId: row.platformSku.platformSkuId
    },
    type: 'bindProducts',
  }
}

const goodsBindColumns = [
  {type: 'selection', showOverflowTooltip: false},
  {type: 'index'},
  {label: '规格信息', prop: 'sku', showOverflowTooltip: false},
  {label: '商品名称', prop: 'goods', showOverflowTooltip: false},
  {label: '来源', prop: 'source', showOverflowTooltip: false},
]
const productsBindColumns = [
  {type: 'selection', showOverflowTooltip: false},
  {type: 'index'},
  {label: '规格信息', prop: 'sku', showOverflowTooltip: false},
  {label: '货品名称', prop: 'goods', showOverflowTooltip: false}
]
const goodsBindRef = ref()
const productsBindRef = ref()

function asyncOk() {
  if (modalObj.value.type === 'bindGoods') {
    const list = goodsBindRef.value.getElTableRef().getSelectionRows()
    const obj = tableData.value.find(it => it.systemItemSku.systemSkuId === modalObj.value.form.systemSkuId)
    obj.systemItemSku.bindPlatformSkuList.push(...list.map(it => {
      return {...it, ...it.platformSku}
    }))
  } else if (modalObj.value.type === 'bindProducts') {
    const list = productsBindRef.value.getElTableRef().getSelectionRows()
    const obj = tableData.value.find(it => it.platformSku.platformSkuId === modalObj.value.form.platformSkuId)
    obj.platformSku.bindSystemSkuList.push(...list.map(it => {
      return {...it, ...it.systemItemSku}
    }))
  }
  modalObj.value.visible = false

}

</script>

<template>
  <div class="goods-relation">
    <div class="tab-select">
      <el-tabs v-model="activeMode" @tab-change="changeMode">
        <el-tab-pane label="货品视角" :name="1">
          <maSearch
            :options="{ showButton: true }"
            :form-options="{ labelWidth: '80px'}"
            :search-items="searchItem"
            @search="getData"
          />
        </el-tab-pane>
        <el-tab-pane label="平台商品视角" :name="2">
          <maSearch
            :options="{ showButton: true }"
            :form-options="{ labelWidth: '80px'}"
            :search-items="searchItem2"
            @search="getData"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="order-table">
      <el-table :data="tableData" style="width: 100%"
                :span-method="objectSpanMethod"
                :header-row-class-name="tableRowClassName">
        <template v-if="activeMode === 1">
          <el-table-column align="center" label="系统货品">
            <el-table-column label="货品名称/编码">
              <template #default="scope">
                <span>{{ scope.row.systemItemName }} </span>
                <span v-if="scope.row.systemOuterId"> / {{ scope.row.systemOuterId }}</span>
              </template>
            </el-table-column>
            <el-table-column label="货品规格/编码/条码">
              <template #default="scope">
                <div class="flex_top">
                  <el-image
                    style="width: 60px; height: 60px;min-width: 60px"
                    :src="scope.row.systemItemSku.systemSkuPicUrl"
                    :hide-on-click-modal="true"
                    :preview-src-list="[scope.row.systemItemSku.systemSkuPicUrl]"
                  />
                  <div class="ml-2">
                    <p>{{ scope.row.systemItemSku.systemSkuName }}</p>
                    <p>{{ scope.row.systemItemSku.systemSkuOuterId }}</p>
                    <p>条码：{{ scope.row.systemItemSku.systemSkuBarCode || '无' }}</p>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="平台商品" align="center">
            <el-table-column label="商品名称" class-name="goods-column">
              <template #default="scope">
                <div v-for="(item,idx) in scope.row.systemItemSku.bindPlatformSkuList" :key="idx" class="goods_line">
                  <span>{{ item.platformItemName }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="规格信息" class-name="goods-column">
              <template #default="scope">
                <div v-for="(item,idx) in scope.row.systemItemSku.bindPlatformSkuList" :key="idx" class="goods_line">
                  <div class="flex_top">
                    <el-image
                      style="width: 60px; height: 60px;min-width: 60px"
                      :src="item.platformSkuPicUrl"
                      :hide-on-click-modal="true"
                      :preview-src-list="[item.platformSkuPicUrl]"
                    />
                    <div class="ml-2">
                      <p>{{ item.platformSkuName }}</p>
                      <p>{{ item.platformSkuOuterId }}</p>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="店铺" class-name="goods-column">
              <template #default="scope">
                <div v-for="(item,idx) in scope.row.systemItemSku.bindPlatformSkuList" :key="idx" class="goods_line">
                  <span>【抖音】</span>
                  <span>{{ item.platformShopName }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作取消" class-name="goods-column" width="80" align="center">
              <template #default="scope">
                <div v-for="(item,idx) in scope.row.systemItemSku.bindPlatformSkuList" :key="idx" class="goods_line">
                  <a @click="cancelGoodsRelation(item,scope.row)">取消关联</a>
                </div>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="操作" width="80" align="center">
            <template #default="scope">
              <a @click="bindGoods(scope.row)">关联平台商品</a>
            </template>
          </el-table-column>
        </template>
        <template v-else>
          <el-table-column label="平台商品" align="center">
            <el-table-column label="商品名称">
              <template #default="scope">
                <span>{{ scope.row.platformItemName }}</span>
                <span v-if="scope.row.platformItemOuterId"> / {{ scope.row.platformItemOuterId }}</span>
              </template>
            </el-table-column>
            <el-table-column label="规格信息">
              <template #default="scope">
                <div class="flex_top">
                  <el-image
                    style="width: 60px; height: 60px;min-width: 60px"
                    :src="scope.row.platformSku.platformSkuPicUrl"
                    :hide-on-click-modal="true"
                    :preview-src-list="[scope.row.platformSku.platformSkuPicUrl]"
                  />
                  <div class="ml-2">
                    <p>{{ scope.row.platformSku.platformSkuName }}</p>
                    <p>{{ scope.row.platformSku.platformSkuOuterId }}</p>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column align="center" label="系统货品">
            <el-table-column label="货品名称/编码" class-name="goods-column">
              <template #default="scope">
                <div v-for="(item,idx) in scope.row.platformSku.bindSystemSkuList" :key="idx" class="goods_line">
                  <span>
                    <span>{{ item.systemItemName }}</span>
                    <span v-if="item.systemOuterId"> / {{ item.systemOuterId }}</span>
                  </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="货品规格/编码/条码" class-name="goods-column">
              <template #default="scope">
                <div v-for="(item,idx) in scope.row.platformSku.bindSystemSkuList" :key="idx" class="goods_line">
                  <div class="flex_top">
                    <el-image
                      style="width: 60px; height: 60px;min-width: 60px"
                      :src="item.systemSkuPicUrl"
                      :hide-on-click-modal="true"
                      :preview-src-list="[item.systemSkuPicUrl]"
                    />
                    <div class="ml-2">
                      <p>{{ item.systemSkuName }}</p>
                      <p>{{ item.systemSkuOuterId }}</p>
                      <p>条码：{{ item.systemSkuBarCode || '无' }}</p>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作取消" class-name="goods-column" width="80" align="center">
              <template #default="scope">
                <div v-for="(item,idx) in scope.row.platformSku.bindSystemSkuList" :key="idx" class="goods_line">
                  <a @click="cancelProductsRelation(item,scope.row)">取消关联</a>
                </div>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="操作" width="80" align="center">
            <template #default="scope">
              <a @click="bindProducts(scope.row)">关联货品</a>
            </template>
          </el-table-column>
        </template>
      </el-table>
    </div>
  </div>

  <el-dialog :title="modalObj.title" v-model="modalObj.visible" :width="modalObj.width" :close-on-click-modal="false">
    <template v-if="modalObj.type === 'bindGoods'">
      <maSearch
        :options="{ showButton: true }"
        :form-options="{ labelWidth: '80px'}"
        :search-items="searchItem3"
        @search="getData"
      />
      <ma-table :data="canChooseGoods" :columns="goodsBindColumns" :span-method="objectSpanMethod2" :max-height="550" ref="goodsBindRef">
        <template #column-source="scope">
          <span>抖音 {{scope.row.platformShopName}}</span>
        </template>
        <template #column-goods="scope">
          <span>{{ scope.row.platformItemName }}</span>
          <span v-if="scope.row.platformItemOuterId"> / {{ scope.row.platformItemOuterId }}</span>
        </template>
        <template #column-sku="scope">
          <div class="flex_top">
            <el-image
              style="width: 40px; height: 40px;min-width: 40px"
              :src="scope.row.platformSku.platformSkuPicUrl"
              :hide-on-click-modal="true"
              :preview-src-list="[scope.row.platformSku.platformSkuPicUrl]"
            />
            <div class="ml-2">
              <p>{{ scope.row.platformSku.platformSkuName }}</p>
              <p>{{ scope.row.platformSku.platformSkuOuterId }}</p>
            </div>
          </div>
        </template>
      </ma-table>
    </template>
    <template v-else-if="modalObj.type === 'bindProducts'">
      <maSearch
        :options="{ showButton: true }"
        :form-options="{ labelWidth: '80px'}"
        :search-items="searchItem4"
        @search="getData"
      />
      <ma-table :data="canChooseProducts" :columns="productsBindColumns" :span-method="objectSpanMethod2"
                :max-height="550" ref="productsBindRef">
        <template #column-goods="scope">
          <span>{{ scope.row.systemItemName }}</span>
          <span v-if="scope.row.systemOuterId"> / {{ scope.row.systemOuterId }}</span>
        </template>
        <template #column-sku="scope">
          <div class="flex_top">
            <el-image
              style="width: 40px; height: 40px;min-width: 40px"
              :src="scope.row.systemItemSku.systemSkuPicUrl"
              :hide-on-click-modal="true"
              :preview-src-list="[scope.row.systemItemSku.systemSkuPicUrl]"
            />
            <div class="ml-2">
              <p>{{ scope.row.systemItemSku.systemSkuName }}</p>
              <p>{{ scope.row.systemItemSku.systemSkuOuterId }}</p>
            </div>
          </div>
        </template>
      </ma-table>
    </template>
    <template #footer>
      <el-button @click="modalObj.visible = false">取 消</el-button>
      <el-button type="primary" @click="asyncOk">保 存</el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss">
.goods-relation {
  margin: 0.75rem;

  .tab-select {
    background: white;
    padding: 0.75rem;
  }

  .order-table {
    background: white;
    padding: 0.75rem 0.75rem 5rem 0.75rem;
    margin: 0.75rem 0;
  }

  .goods-column {
    .goods_line {
      height: 80px;
      border-bottom: 1px solid #ebeef5;
      display: flex;
      align-items: center;
    }

    .goods_line:last-child {
      border-bottom: none;
    }
  }


  .first-row-1 {
    .el-table__cell:first-child {
      background-color: #ffebee !important;
      color: #e53935 !important;
    }

    .el-table__cell:nth-child(2) {
      background-color: #f5f9ff !important;
      color: #2962ff !important;
    }
  }

  .first-row-2 {
    .el-table__cell:nth-child(2) {
      background-color: #ffebee !important;
      color: #e53935 !important;
    }

    .el-table__cell:first-child {
      background-color: #f5f9ff !important;
      color: #2962ff !important;
    }
  }
}
</style>

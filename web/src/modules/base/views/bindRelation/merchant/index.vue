<script setup>
import {useMessage} from '@/hooks/useMessage.ts'
import {UserLoginLog} from "~/base/api/log.ts";

defineOptions({ name: 'distribution:merchant' })


const t = useTrans().globalTrans
const proTableRef = ref()
const selections = ref([])
const msg = useMessage()

function clickBindMerchant(){
  modalObj.value = {
    visible: true,
    title: '绑定商家',
    width: '30%',
    form: {},
    type: 'bindMerchant',
  }
}

const options = ref({
  // 表格距离底部的像素偏移适配
  adaptionOffsetBottom: 161,
  header: {
    mainTitle: () => '商家管理',
  },
  // 表格参数
  tableOptions: {
    on: {
      // 表格选择事件
      onSelectionChange: (selection) => selections.value = selection,
    },
  },
  // 搜索参数
  searchOptions: {
    fold: true,
    text: {
      searchBtn: () => t('crud.search'),
      resetBtn: () => t('crud.reset'),
      isFoldBtn: () => t('crud.searchFold'),
      notFoldBtn: () => t('crud.searchUnFold'),
    },
  },
  // 搜索表单参数
  searchFormOptions: { labelWidth: '90px' },
  // 请求配置
  requestOptions: {
    api: UserLoginLog.page,
    responseDataHandler: (res) => {
      res.list.forEach((it, idx) => {
        it.createTime = it.login_time
        it.mobile = 18898652123 + idx + ''
        it.username = '商家' + it.mobile
      })
    },
  },
})
// 架构配置
const schema = ref({
  // 搜索项
  searchItems: [
    {
      label: () => '商家名称',
      prop: 'username',
      render: 'input',
    }
  ],
  // 表格列
  tableColumns: [
    // 多选列
    { type: 'selection', showOverflowTooltip: false, label: () => t('crud.selection') },
    // 索引序号列
    { type: 'index' },
    // 普通列
    { label: () => '商家手机号', prop: 'mobile' },
    { label: () => '商家名称', prop: 'username' },
    { label: () => '绑定时间', prop: 'createTime' },
    // 操作列
    {
      type: 'operation',
      label: () => '操作',
      operationConfigure: {
        type: 'tile',
        actions: [
          {
            name: 'del',
            text: () => '取消关联',
            onClick: ({ row }, proxy) => {
              msg.delConfirm('确定后立即取消无需商家审核，解绑后，商家给您分配的未发货的订单等将被清除，确定继续解绑吗？','取消关联商家').then(async () => {

              })
            },
          },
          {
            name: 'edit',
            linkProps: { underline: false },
            text: () => '编辑名称',
            onClick: ({ row }, proxy) => {
              modalObj.value = {
                visible: true,
                title: '编辑商家名称',
                width: '30%',
                form: {username: row.username},
                type: 'updateName',
              }
            },
          },
        ],
      },
    },
  ],
})

const modalObj = ref({
  visible: false,
  title: '',
  width: '500px',
  form: {},
  type: '',
})

const bindModel = ref({
  phone: ''
})

function asyncOk(){

}
</script>

<template>
  <div class="mine-layout pt-3">
    <MaProTable ref="proTableRef" :options="options" :schema="schema">
      <template #toolbarLeft>
        <el-button type="primary" plain @click="clickBindMerchant">
          绑定商家
        </el-button>
      </template>
    </MaProTable>
  </div>
  <el-dialog :title="modalObj.title" v-model="modalObj.visible" :width="modalObj.width" :close-on-click-modal="false">
    <template v-if="modalObj.type === 'bindMerchant'">
      <h3>方式一：手机号绑定</h3>
      <div class="flex_center mt-2">
        <span>商家手机号：</span>
        <el-input class="ml-2" style="width: 300px" placeholder="输入商家手机号" v-model="bindModel.phone"/>
        <el-button type="primary" class="ml-2">确定</el-button>
      </div>
      <h3 class="mt-10">方式二：复制链接邀请</h3>
      <p class="red mt-1">发送邀请链接给商家，商家注册登录后自动生成绑定关系</p>
      <div class="flex_top mt-2">
        <span>邀请链接：</span>
        <el-input type="textarea" class="ml-2" style="width: 400px" :rows="4"/>
      </div>
      <div class="flex_center mt-2">
        <el-button class="ml-auto">复制链接</el-button>
      </div>
    </template>
    <template v-else-if="modalObj.type === 'updateName'">
      <div class="flex_center mt-2">
        <span>商家名称：</span>
        <el-input class="ml-2" style="width: 300px" placeholder="输入商家名称" v-model="modalObj.form.username"/>
      </div>
    </template>
    <template #footer v-if="['updateName'].includes(modalObj.type)">
      <el-button @click="modalObj.visible = false">取 消</el-button>
      <el-button type="primary" @click="asyncOk">保 存</el-button>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
</style>

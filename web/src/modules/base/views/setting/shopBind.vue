<script setup>
import passwordForm from '@/modules/base/views/uc/components/password-form.vue'

defineOptions({name: 'setting:shop'})

const shopList = ref([])

const modalObj = ref({
  visible: false,
  title: '',
  width: '500px',
  form: {},
  type: '',
})

function showCreateModal() {
  modalObj.value = {
    visible: true,
    title: '绑定多平台、多店铺',
    width: '600px',
    form: {},
    type: 'bindShops',
  }
}

function showUpdatePwdModal() {
  modalObj.value = {
    visible: true,
    title: '修改密码',
    width: '600px',
    form: {},
    type: 'updatePwd',
  }
}

const partnerList = ['dy', 'taobao_top', 'jd', 'ks', 'wx', 'pdd', 'albb']

function handleSelectBindPlatform(platformCode){
  if(modalObj.value.form.platformCode === platformCode) return
  modalObj.value.form.platformCode = platformCode
}

function asyncOk(){

}

function goAuth(){

}

const passwordFormRef = ref()

function submitchangePwd(){
  passwordFormRef.value.submit()
}

</script>

<template>
  <div class="shop_bind">
    <div class="account-info">
      <h4>账号信息</h4>
      <div class="flex_center mt-5">
        <ma-svg-icon name="material-symbols:account-circle-full" style="font-size: 30px"/>
        <span class="bold_font ml-1">138****1786</span>
        <span class="grey ml-10">账号ID：</span>
        <span>MWWE01909YD038VR</span>
        <span class="ml-10 grey">手机号：</span>
        <span>138****1786</span>
        <span class="ml-10 grey">密码：</span>
        <span>已设置</span>
        <ma-svg-icon @click="showUpdatePwdModal" name="material-symbols:edit-outline" style="font-size: 20px" class="ml-1 blue"/>
      </div>
    </div>
    <div class="shop-list-content">
      <div>
        <el-button type="primary" @click="showCreateModal">绑定新店铺</el-button>
        <el-button type="danger" plain :disabled="!shopList.length">批量解绑</el-button>
      </div>
      <el-table class="mt-2">
        <el-table-column width="40" type="selection"/>
        <el-table-column label="序号" :show-overflow-tooltip="false">
          <template #default="scope">
          </template>
        </el-table-column>
        <el-table-column label="平台" :show-overflow-tooltip="false">
        </el-table-column>
        <el-table-column label="店铺" :show-overflow-tooltip="false">
        </el-table-column>
        <el-table-column label="订购到期时间" :show-overflow-tooltip="false">
        </el-table-column>
        <el-table-column label="授权状态" :show-overflow-tooltip="false">
        </el-table-column>
        <el-table-column label="操作" width="100">
        </el-table-column>
      </el-table>
    </div>
    <el-dialog :title="modalObj.title" v-model="modalObj.visible" :width="modalObj.width" :close-on-click-modal="false">
      <template v-if="modalObj.type === 'bindShops'">
        <p class="grey">点击想要绑定的平台店铺进行授权</p>
        <el-row class="bind_shop_list">
          <el-col :span="6" v-for="(item, index) in partnerList"  :key="item">
            <a :class="[item, { active : modalObj.form.platformCode === item}]" @click="handleSelectBindPlatform(item)"></a>
          </el-col>
        </el-row>
      </template>
      <template v-else-if="modalObj.type === 'updatePwd'">
        <passwordForm ref="passwordFormRef" @submit="submitchangePwd" />
      </template>
      <template #footer>
        <template v-if="modalObj.type === 'bindShops'">
          <el-button type="primary" @click="goAuth">前往授权</el-button>
        </template>
        <template v-else>
          <el-button @click="modalObj.visible = false">取 消</el-button>
          <el-button type="primary" @click="asyncOk">保 存</el-button>
        </template>
      </template>
    </el-dialog>
  </div>

</template>


<style scoped lang="scss">
.shop_bind {
  margin: 0.75rem;

  .account-info {
    border: 1px solid #d6d6d6;
    border-radius: 4px;
    padding: 0.75rem;
    background: #ffffff;
  }

  .shop-list-content {
    margin-top: 0.75rem;
    padding: 0.75rem;
    background: white;
  }
}

.bind_shop_list {
  margin-top: 1rem;
  a {
    display: inline-block;
    width: 100px;
    height: 50px;
    box-sizing: border-box;
    cursor: pointer;
    border-radius: 3px;
    margin: 4px;
    transition: all .3s;
    background-size: cover;

    &:hover {
      box-shadow: 0 0 2px #ccc;
    }

    &.active {
      box-shadow: 0 0 3px 2px var(--el-color-primary);
    }

    &.dy {
      background-image: url(../../../../assets/images/logo-dy.png);
    }

    &.taobao_top {
      background-image: url(../../../../assets/images/logo-tb.png);
    }

    &.jd {
      background-image: url(../../../../assets/images/logo-jd.png);
    }

    &.ks {
      background-image: url(../../../../assets/images/logo-ks.png);
    }

    &.wx {
      background-image: url(../../../../assets/images/logo-wx.png);
    }

    &.pdd {
      background-image: url(../../../../assets/images/logo-pdd.png);
    }

    &.albb {
      background-image: url(../../../../assets/images/logo-1688.png);
    }
  }
}

</style>

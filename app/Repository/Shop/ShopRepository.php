<?php

namespace App\Repository\Shop;

use App\Repository\IRepository;
use App\Model\Shop\Shop as Model;
use Hyperf\Database\Model\Builder;


class ShopRepository extends IRepository
{
    public function __construct(
        protected readonly Model $model
    ) {}

    public function handleSearch(Builder $query, array $params): Builder
    {
                                    
        if (isset($params['id'])) {
            $query->where('id', $params['id']);
        }
        if (isset($params['ids'])) {
            $query->whereIn('id', $params['ids']);
        }
        if (isset($params['shop_name'])) {
            $query->where('shop_name', $params['shop_name']);
        }

        return $query;
    }
}

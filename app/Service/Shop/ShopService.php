<?php

namespace App\Service\Shop;

use App\Service\IService;
use App\Repository\Shop\ShopRepository as Repository;



class ShopService extends IService
{
    public function __construct(
        protected readonly Repository $repository
    ) {}

    public function getRepository(): Repository
    {
        return $this->repository;
    }

    public function bindList(\App\Http\CurrentUser $currentUser, array $getRequestData, int $getCurrentPage, int $getPageSize)
    {
        $user = $currentUser->user();
        $shopIds = $user->shopBinds()->pluck('shop_id')->toArray();
        $params = [
            'ids' => $shopIds,
        ];
        return $this->repository->list($params);
    }
}
